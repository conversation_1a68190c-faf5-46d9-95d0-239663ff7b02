# utils/functional_gating_viz.py
"""
图 4c: 由智能体间邻近度驱动的功能性门控 (Functional Gating Driven by Inter-Agent Proximity)
可视化relational_head激活与智能体间距离的关系
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm
from scipy.ndimage import gaussian_filter
from scipy.stats import pearsonr
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional
import time

# 导入项目模块
import sys
import os
# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble

# 设置期刊级别的matplotlib风格
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],
    'font.size': 10,
    'axes.labelsize': 12,
    'axes.titlesize': 14,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 9,
    'figure.titlesize': 16,
    'figure.dpi': 150,
    'axes.linewidth': 0.8,
    'xtick.major.width': 0.8,
    'ytick.major.width': 0.8,
    'axes.spines.top': False,
    'axes.spines.right': False,
})

@dataclass
class ProximityData:
    """存储邻近度分析数据"""
    distances: np.ndarray = field(default_factory=lambda: np.array([]))
    activations: np.ndarray = field(default_factory=lambda: np.array([]))
    self_positions: np.ndarray = field(default_factory=lambda: np.array([]))
    peer_positions: np.ndarray = field(default_factory=lambda: np.array([]))
    relative_positions: np.ndarray = field(default_factory=lambda: np.array([]))

class TrajectoryGenerator:
    """轨迹生成器，与原代码保持一致"""
    def __init__(self, config: Config):
        self.maze_size = config.ENV_SIZE
        self.seq_len = config.SEQUENCE_LENGTH
        self.start_S = np.array([self.maze_size * 0.5, self.maze_size * 0.2])
        self.end_A = np.array([self.maze_size * 0.2, self.maze_size * 0.8])
        self.end_B = np.array([self.maze_size * 0.8, self.maze_size * 0.8])

    def _generate_path(self, start: np.ndarray, end: np.ndarray, moving: bool) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        if not moving:
            positions = np.tile(start, (self.seq_len, 1))
            velocities = np.zeros((self.seq_len, 2))
            angular_velocities = np.zeros(self.seq_len)
            return positions, velocities, angular_velocities

        # 创建基础直线路径
        base_positions = np.array([start + (t / max(1, self.seq_len - 1)) * (end - start) for t in range(self.seq_len)])
        
        # 创建平滑的随机扰动
        direction = (end - start) / (np.linalg.norm(end - start) + 1e-6)
        perpendicular = np.array([-direction[1], direction[0]])
        
        # 随机正弦波参数
        amplitude = np.random.uniform(0.5, 1.5)
        frequency = np.random.uniform(1.5, 2.5)
        phase = np.random.uniform(0, np.pi)
        
        t = np.linspace(0, 1, self.seq_len)
        sine_offset = amplitude * np.sin(2 * np.pi * frequency * t + phase)
        
        # 将扰动应用到垂直于运动方向的轴上
        positions = base_positions + sine_offset[:, np.newaxis] * perpendicular[np.newaxis, :]
        positions = np.clip(positions, 0.5, self.maze_size - 0.5)

        velocities = np.diff(positions, axis=0, prepend=positions[0:1])
        angles = np.arctan2(velocities[:, 1], velocities[:, 0])
        angular_velocities = np.diff(np.unwrap(angles), prepend=angles[0:1])
        return positions, velocities, angular_velocities

    def get_trajectories_for_condition(self, condition: int, num_reps: int = 50) -> Dict:
        """生成指定条件下的轨迹，特别优化用于功能性门控分析"""
        all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
        all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []
        movement_patterns = {1: (True, False), 2: (False, True), 3: (True, True), 4: (False, False)}
        self_moving, peer_moving = movement_patterns[condition]

        for i in range(num_reps):
            if self_moving:
                # Self移动：从start_S到随机目标
                target = self.end_A if i % 2 == 0 else self.end_B
                self_start, self_end = self.start_S, target
            else:
                # Self静止：固定在中心位置
                center_pos = np.array([self.maze_size * 0.5, self.maze_size * 0.5])
                self_start, self_end = center_pos, center_pos

            if peer_moving:
                # Peer移动：从start_S到随机目标
                target = self.end_A if (i + 1) % 2 == 0 else self.end_B
                peer_start, peer_end = self.start_S, target
            else:
                # Peer静止：在距离self -7.5到7.5单位范围内的随机位置
                if self_moving:
                    # 如果self在移动，peer相对于self的起始位置随机分布
                    reference_pos = self_start
                else:
                    # 如果self静止，peer相对于self的固定位置随机分布
                    reference_pos = self_start

                # 生成-7.5到7.5范围内的随机相对位置
                relative_x = np.random.uniform(-7.5, 7.5)
                relative_y = np.random.uniform(-7.5, 7.5)
                peer_pos = reference_pos + np.array([relative_x, relative_y])

                # 确保peer位置在环境边界内
                peer_pos = np.clip(peer_pos, 0.5, self.maze_size - 0.5)
                peer_start, peer_end = peer_pos, peer_pos

            self_pos, self_vel, self_ang_vel = self._generate_path(self_start, self_end, self_moving)
            peer_pos, peer_vel, peer_ang_vel = self._generate_path(peer_start, peer_end, peer_moving)

            all_self_pos.append(self_pos); all_self_vel.append(self_vel); all_self_ang_vel.append(self_ang_vel)
            all_peer_pos.append(peer_pos); all_peer_vel.append(peer_vel); all_peer_ang_vel.append(peer_ang_vel)

        return {
            'self_pos': np.array(all_self_pos), 'self_vel': np.array(all_self_vel), 'self_ang_vel': np.array(all_self_ang_vel),
            'peer_pos': np.array(all_peer_pos), 'peer_vel': np.array(all_peer_vel), 'peer_ang_vel': np.array(all_peer_ang_vel),
        }

    def get_proximity_analysis_trajectories(self, num_reps: int = 200) -> Dict:
        """专门为邻近度分析生成轨迹，确保partner位置完全随机分布"""
        all_self_pos, all_self_vel, all_self_ang_vel = [], [], []
        all_peer_pos, all_peer_vel, all_peer_ang_vel = [], [], []

        # Self固定在中心位置
        center_pos = np.array([self.maze_size * 0.5, self.maze_size * 0.5])

        for i in range(num_reps):
            # Self始终静止在中心
            self_start, self_end = center_pos, center_pos

            # Partner在以self为中心的圆形区域内随机分布
            # 扩展最大半径到21米以覆盖更大的距离范围
            max_radius = min(21.0, self.maze_size * 0.4)  # 确保不超出环境边界

            # 均匀分布的半径和角度
            radius = np.sqrt(np.random.uniform(0, max_radius**2))  # 平方根确保面积均匀
            angle = np.random.uniform(0, 2 * np.pi)

            # 转换为笛卡尔坐标
            relative_x = radius * np.cos(angle)
            relative_y = radius * np.sin(angle)

            peer_pos = center_pos + np.array([relative_x, relative_y])

            # 确保peer位置在环境边界内
            peer_pos = np.clip(peer_pos, 0.5, self.maze_size - 0.5)
            peer_start, peer_end = peer_pos, peer_pos

            self_pos, self_vel, self_ang_vel = self._generate_path(self_start, self_end, False)
            peer_pos, peer_vel, peer_ang_vel = self._generate_path(peer_start, peer_end, False)

            all_self_pos.append(self_pos); all_self_vel.append(self_vel); all_self_ang_vel.append(self_ang_vel)
            all_peer_pos.append(peer_pos); all_peer_vel.append(peer_vel); all_peer_ang_vel.append(peer_ang_vel)

        return {
            'self_pos': np.array(all_self_pos), 'self_vel': np.array(all_self_vel), 'self_ang_vel': np.array(all_self_ang_vel),
            'peer_pos': np.array(all_peer_pos), 'peer_vel': np.array(all_peer_vel), 'peer_ang_vel': np.array(all_peer_ang_vel),
        }

def create_proximity_heatmap(relative_positions: np.ndarray, activations: np.ndarray, 
                           grid_size: int = 50, max_distance: float = 8.0, sigma: float = 1.0) -> np.ndarray:
    """
    创建基于相对位置的激活热图
    
    Args:
        relative_positions: 相对位置 (N, 2) - peer相对于self的位置
        activations: 对应的激活值 (N,)
        grid_size: 网格大小
        max_distance: 最大距离范围
        sigma: 高斯平滑参数
    
    Returns:
        平滑的热图
    """
    heatmap = np.zeros((grid_size, grid_size))
    counts = np.zeros((grid_size, grid_size))
    
    # 将相对位置映射到网格坐标
    for i in range(len(relative_positions)):
        rel_pos = relative_positions[i]
        act = activations[i]
        
        # 将相对位置转换为网格坐标（中心为观察者位置）
        grid_x = int(np.clip((rel_pos[0] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))
        grid_y = int(np.clip((rel_pos[1] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))
        
        heatmap[grid_y, grid_x] += act
        counts[grid_y, grid_x] += 1
    
    # 计算平均激活
    with np.errstate(divide='ignore', invalid='ignore'):
        avg_heatmap = np.where(counts > 0, heatmap / counts, 0)
    
    # 高斯平滑
    return gaussian_filter(avg_heatmap, sigma=sigma)

def analyze_proximity_gating(model: SocialGridCellNetwork, config: Config, device: torch.device, 
                           num_reps: int = 100) -> Dict[int, ProximityData]:
    """
    分析邻近度驱动的功能性门控
    
    Args:
        model: 训练好的模型
        config: 配置对象
        device: 计算设备
        num_reps: 每个条件的重复次数
    
    Returns:
        每个条件的邻近度数据
    """
    trajectory_gen = TrajectoryGenerator(config)
    proximity_data = {}
    
    model.eval()
    with torch.no_grad():
        for condition in tqdm(range(1, 5), desc="Analyzing Proximity Gating"):
            trajs = trajectory_gen.get_trajectories_for_condition(condition, num_reps=num_reps)
            
            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
            
            # 获取relational_activations的平均激活
            relational_activations = outputs['relational_activations'].cpu().numpy()  # (batch, seq, 64)
            mean_activations = np.mean(relational_activations, axis=-1)  # (batch, seq) - 对64个神经元求平均
            
            # 计算距离和相对位置
            self_pos = trajs['self_pos']  # (batch, seq, 2)
            peer_pos = trajs['peer_pos']  # (batch, seq, 2)
            
            distances = np.linalg.norm(self_pos - peer_pos, axis=-1)  # (batch, seq)
            relative_positions = peer_pos - self_pos  # (batch, seq, 2) - peer相对于self的位置
            
            # 展平数据用于分析
            distances_flat = distances.flatten()
            activations_flat = mean_activations.flatten()
            relative_positions_flat = relative_positions.reshape(-1, 2)
            
            proximity_data[condition] = ProximityData(
                distances=distances_flat,
                activations=activations_flat,
                self_positions=self_pos.reshape(-1, 2),
                peer_positions=peer_pos.reshape(-1, 2),
                relative_positions=relative_positions_flat
            )
    
    return proximity_data

def find_best_relational_neurons(model: SocialGridCellNetwork, config: Config, device: torch.device,
                                num_reps: int = 50) -> List[int]:
    """
    找到最适合展示的relational neurons（激活强度高且与距离相关性强的神经元）

    Returns:
        最佳神经元的索引列表
    """
    trajectory_gen = TrajectoryGenerator(config)
    neuron_scores = []

    model.eval()
    with torch.no_grad():
        # 使用条件3（both moving）来评估神经元
        trajs = trajectory_gen.get_trajectories_for_condition(3, num_reps=num_reps)

        # 准备输入数据
        self_vel_input = torch.cat([
            torch.from_numpy(trajs['self_vel']).float(),
            torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
        ], dim=-1).to(device)

        self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
        self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)

        peer_vel_input = torch.cat([
            torch.from_numpy(trajs['peer_vel']).float(),
            torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
        ], dim=-1).to(device)

        peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
        peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

        # 前向传播
        outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
        relational_activations = outputs['relational_activations'].cpu().numpy()  # (batch, seq, 64)

        # 计算距离
        self_pos = trajs['self_pos']
        peer_pos = trajs['peer_pos']
        distances = np.linalg.norm(self_pos - peer_pos, axis=-1).flatten()

        # 评估每个神经元
        for neuron_idx in range(relational_activations.shape[-1]):
            activations = relational_activations[:, :, neuron_idx].flatten()

            # 计算评分：激活强度 + 与距离的相关性强度
            max_activation = np.max(activations)
            mean_activation = np.mean(activations)
            correlation, p_value = pearsonr(distances, activations)

            # 综合评分：激活强度 + 相关性强度（取绝对值）
            score = mean_activation * 0.5 + abs(correlation) * 0.5

            neuron_scores.append((neuron_idx, score, max_activation, correlation, p_value))

    # 按评分排序，返回前几个最佳神经元
    neuron_scores.sort(key=lambda x: x[1], reverse=True)
    best_neurons = [score[0] for score in neuron_scores[:5]]  # 返回前5个最佳神经元

    print("Best relational neurons for visualization:")
    for i, (neuron_idx, score, max_act, corr, p_val) in enumerate(neuron_scores[:5]):
        print(f"  {i+1}. Neuron {neuron_idx}: Score={score:.3f}, Max_Act={max_act:.3f}, Corr={corr:.3f}, p={p_val:.3f}")

    return best_neurons

def visualize_functional_gating(proximity_data: Dict, config: Config,
                              output_dir: str, neuron_idx: Optional[int] = None):
    """
    创建图4c: 由智能体间邻近度驱动的功能性门控可视化

    Args:
        proximity_data: 邻近度分析数据
        config: 配置对象
        output_dir: 输出目录
        neuron_idx: 特定神经元索引（用于标题）
    """
    # 获取邻近度分析数据
    if "proximity" in proximity_data:
        data = proximity_data["proximity"]
    else:
        # 如果没有专门的邻近度数据，使用条件3
        condition = 3
        data = proximity_data[condition]

    # 创建图形
    fig = plt.figure(figsize=(16, 6))

    # 左侧面板：激活热图（相对位置）
    ax1 = plt.subplot(1, 2, 1)

    # 创建相对位置热图
    max_distance = 8.0  # 最大显示距离
    heatmap = create_proximity_heatmap(data.relative_positions, data.activations,
                                     grid_size=60, max_distance=max_distance, sigma=1.2)

    # 绘制热图
    extent = [-max_distance, max_distance, -max_distance, max_distance]
    im1 = ax1.imshow(heatmap, cmap='hot', origin='lower', extent=extent,
                     aspect='equal', interpolation='bilinear')

    # 标记观察者位置（中心）
    ax1.scatter(0, 0, color='cyan', s=100, marker='*', edgecolors='black',
               linewidth=2, label='Observer (Self)', zorder=5)

    # 添加距离圆圈作为参考
    for radius in [2, 4, 6]:
        circle = plt.Circle((0, 0), radius, fill=False, color='white',
                          linestyle='--', alpha=0.6, linewidth=1)
        ax1.add_patch(circle)
        ax1.text(radius*0.7, radius*0.7, f'{radius}m', color='white',
                fontsize=8, ha='center', va='center')

    ax1.set_xlabel('Relative X Position (m)', fontsize=12)
    ax1.set_ylabel('Relative Y Position (m)', fontsize=12)
    ax1.set_title('Relational Head Activation Heatmap\n(Partner Position Relative to Observer)',
                 fontsize=12, pad=15)
    ax1.legend(loc='upper right', frameon=True, fancybox=False, shadow=False, fontsize=9)
    ax1.grid(True, linestyle=':', alpha=0.3, color='white')

    # 添加colorbar
    cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
    cbar1.set_label('Mean Relational Activation', rotation=270, labelpad=15, fontsize=11)

    # 右侧面板：距离分布柱状图 (Nature子刊风格)
    ax2 = plt.subplot(1, 2, 2)

    # 过滤数据以提高可视化效果
    max_dist_plot = 10.0
    valid_mask = data.distances <= max_dist_plot
    distances_plot = data.distances[valid_mask]
    activations_plot = data.activations[valid_mask]

    # 创建距离分箱
    n_bins = 20
    bin_edges = np.linspace(0, max_dist_plot, n_bins + 1)
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2

    # 计算每个分箱的平均激活和标准误差
    bin_means = []
    bin_stds = []
    bin_counts = []

    for i in range(n_bins):
        mask = (distances_plot >= bin_edges[i]) & (distances_plot < bin_edges[i + 1])
        if np.sum(mask) > 0:
            bin_activations = activations_plot[mask]
            bin_means.append(np.mean(bin_activations))
            bin_stds.append(np.std(bin_activations) / np.sqrt(len(bin_activations)))  # 标准误差
            bin_counts.append(len(bin_activations))
        else:
            bin_means.append(0)
            bin_stds.append(0)
            bin_counts.append(0)

    bin_means = np.array(bin_means)
    bin_stds = np.array(bin_stds)
    bin_counts = np.array(bin_counts)

    # 创建柱状图 (Nature风格)
    bar_width = bin_centers[1] - bin_centers[0] * 0.8
    bars = ax2.bar(bin_centers, bin_means, width=bar_width,
                   alpha=0.7, color='steelblue', edgecolor='black', linewidth=0.5)

    # 添加误差棒
    ax2.errorbar(bin_centers, bin_means, yerr=bin_stds, fmt='none',
                color='black', capsize=2, capthick=1, alpha=0.8)

    # 计算并绘制顶部拟合曲线
    if len(distances_plot) > 10:
        # 只使用有数据的分箱进行拟合
        valid_bins = bin_counts > 0
        if np.sum(valid_bins) > 3:
            # 使用多项式拟合
            poly_features = PolynomialFeatures(degree=2)
            X_poly = poly_features.fit_transform(bin_centers[valid_bins].reshape(-1, 1))
            poly_reg = LinearRegression()
            poly_reg.fit(X_poly, bin_means[valid_bins])

            # 生成平滑的拟合线
            dist_smooth = np.linspace(0, max_dist_plot, 100)
            X_smooth_poly = poly_features.transform(dist_smooth.reshape(-1, 1))
            activation_smooth = poly_reg.predict(X_smooth_poly)

            # 绘制拟合曲线在柱状图顶部
            ax2.plot(dist_smooth, activation_smooth, color='red', linewidth=3,
                    label='Polynomial Fit', alpha=0.9, zorder=10)

        # 计算整体相关系数
        correlation, p_value = pearsonr(distances_plot, activations_plot)

        # 添加统计信息 (Nature风格)
        stats_text = f'r = {correlation:.3f}\np = {p_value:.2e}\nn = {len(distances_plot):,}'
        ax2.text(0.95, 0.95, stats_text, transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', horizontalalignment='right',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9, edgecolor='gray'))

    ax2.set_xlabel('Inter-Agent Distance (m)', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Mean Neural Activation', fontsize=12, fontweight='bold')
    ax2.set_title('Neural Activation Distribution\nby Inter-Agent Distance',
                 fontsize=12, pad=15, fontweight='bold')

    # Nature风格的图例和网格
    handles, labels = ax2.get_legend_handles_labels()
    if labels and 'Polynomial Fit' in labels:
        ax2.legend(loc='upper right', frameon=True, fancybox=False, shadow=False,
                  fontsize=9, edgecolor='black')

    ax2.grid(True, linestyle=':', alpha=0.3, color='gray')
    ax2.set_xlim(0, max_dist_plot)
    ax2.set_ylim(bottom=0)

    # 设置坐标轴样式 (Nature风格)
    ax2.spines['top'].set_visible(False)
    ax2.spines['right'].set_visible(False)
    ax2.spines['left'].set_linewidth(1.2)
    ax2.spines['bottom'].set_linewidth(1.2)

    # 设置整体标题
    title_suffix = f" (Neuron {neuron_idx})" if neuron_idx is not None else ""
    fig.suptitle(f'Functional Gating Driven by Inter-Agent Proximity{title_suffix}',
                fontsize=16, fontweight='bold', y=0.98)

    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(top=0.90, wspace=0.3)

    # 保存图形
    filename = f'functional_gating_proximity'
    if neuron_idx is not None:
        filename += f'_neuron_{neuron_idx}'
    filename += '.pdf'

    save_path = os.path.join(output_dir, filename)
    plt.savefig(save_path, format='pdf', bbox_inches='tight', dpi=300,
                facecolor='white', edgecolor='none')
    plt.close(fig)

    print(f"Functional gating visualization saved to: {save_path}")

def visualize_individual_neuron_gating(model: SocialGridCellNetwork, config: Config, device: torch.device,
                                     neuron_idx: int, output_dir: str, num_reps: int = 100):
    """
    为特定的relational neuron创建功能性门控可视化

    Args:
        model: 训练好的模型
        config: 配置对象
        device: 计算设备
        neuron_idx: 要可视化的神经元索引
        output_dir: 输出目录
        num_reps: 轨迹重复次数
    """
    trajectory_gen = TrajectoryGenerator(config)

    model.eval()
    with torch.no_grad():
        # 使用专门的邻近度分析轨迹生成方法
        trajs = trajectory_gen.get_proximity_analysis_trajectories(num_reps=num_reps)

        # 准备输入数据
        self_vel_input = torch.cat([
            torch.from_numpy(trajs['self_vel']).float(),
            torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
        ], dim=-1).to(device)

        self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
        self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)

        peer_vel_input = torch.cat([
            torch.from_numpy(trajs['peer_vel']).float(),
            torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
        ], dim=-1).to(device)

        peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
        peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

        # 前向传播
        outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)

        # 获取特定神经元的激活
        relational_activations = outputs['relational_activations'].cpu().numpy()  # (batch, seq, 64)
        neuron_activations = relational_activations[:, :, neuron_idx]  # (batch, seq)

        # 计算距离和相对位置
        self_pos = trajs['self_pos']
        peer_pos = trajs['peer_pos']
        distances = np.linalg.norm(self_pos - peer_pos, axis=-1)
        relative_positions = peer_pos - self_pos

        # 创建ProximityData对象（使用条件标识符"proximity"而不是"3"）
        proximity_data = {"proximity": ProximityData(
            distances=distances.flatten(),
            activations=neuron_activations.flatten(),
            self_positions=self_pos.reshape(-1, 2),
            peer_positions=peer_pos.reshape(-1, 2),
            relative_positions=relative_positions.reshape(-1, 2)
        )}

        # 创建可视化
        visualize_functional_gating(proximity_data, config, output_dir, neuron_idx=neuron_idx)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Visualize Functional Gating Driven by Inter-Agent Proximity")
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to the trained best_model.pth file.')
    parser.add_argument('--output_dir', type=str, default='functional_gating_viz',
                       help='Directory to save visualizations.')
    parser.add_argument('--num_reps', type=int, default=100,
                       help='Number of trajectory repetitions for analysis.')
    parser.add_argument('--specific_neuron', type=int, default=None,
                       help='Specific relational neuron index to visualize (optional).')
    parser.add_argument('--find_best_neurons', action='store_true',
                       help='Find and visualize the best relational neurons.')
    parser.add_argument('--save_all_neurons', action='store_true',
                       help='Save visualizations for all 64 relational neurons.')
    args = parser.parse_args()

    start_time = time.time()
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载配置和模型
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    print("Loading model...")
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N,
        scale=config.PLACE_CELLS_SCALE,
        pos_min=0, pos_max=config.ENV_SIZE,
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N,
        concentration=config.HD_CELLS_CONCENTRATION,
        seed=config.SEED
    )

    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)

    # 加载模型权重
    state_dict = torch.load(args.model_path, map_location=device, weights_only=False)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()

    print(f"Model loaded. Relational head has {model.relational_head[0].out_features} neurons.")

    if args.save_all_neurons:
        print("Creating visualizations for all 64 relational neurons...")
        total_neurons = model.relational_head[0].out_features  # 应该是64

        # 创建子目录存放所有神经元的图
        all_neurons_dir = os.path.join(args.output_dir, "all_neurons")
        os.makedirs(all_neurons_dir, exist_ok=True)

        print(f"This will create {total_neurons} visualizations. This may take several minutes...")

        # 为了演示，我们可以限制数量或者保存全部
        neurons_to_save = min(total_neurons, 10) if args.num_reps < 50 else total_neurons
        print(f"Saving {neurons_to_save} neurons...")

        for neuron_idx in range(neurons_to_save):
            print(f"Visualizing neuron {neuron_idx} ({neuron_idx+1}/{total_neurons})...", end=' ')
            try:
                visualize_individual_neuron_gating(model, config, device, neuron_idx,
                                                 all_neurons_dir, num_reps=args.num_reps)
                print("✓")
            except Exception as e:
                print(f"✗ Error: {e}")

        print(f"All {total_neurons} neuron visualizations saved in '{all_neurons_dir}'")

    elif args.find_best_neurons:
        print("Finding best relational neurons for visualization...")
        best_neurons = find_best_relational_neurons(model, config, device, num_reps=args.num_reps)

        print("Creating visualizations for best neurons...")
        for i, neuron_idx in enumerate(best_neurons[:3]):  # 可视化前3个最佳神经元
            print(f"Visualizing neuron {neuron_idx} ({i+1}/3)...")
            visualize_individual_neuron_gating(model, config, device, neuron_idx,
                                             args.output_dir, num_reps=args.num_reps)

    elif args.specific_neuron is not None:
        print(f"Creating visualization for specific neuron {args.specific_neuron}...")
        visualize_individual_neuron_gating(model, config, device, args.specific_neuron,
                                         args.output_dir, num_reps=args.num_reps)

    else:
        print("Analyzing proximity-driven functional gating (using mean activation across all neurons)...")
        proximity_data = analyze_proximity_gating(model, config, device, num_reps=args.num_reps)

        print("Creating functional gating visualization...")
        visualize_functional_gating(proximity_data, config, args.output_dir)

        # 额外分析：显示各条件下的统计信息
        print("\n--- Proximity Analysis Results ---")
        for condition, data in proximity_data.items():
            condition_names = {1: "Self Moving, Peer Static", 2: "Peer Moving, Self Static",
                             3: "Both Moving", 4: "Both Static"}

            correlation, p_value = pearsonr(data.distances, data.activations)
            mean_distance = np.mean(data.distances)
            mean_activation = np.mean(data.activations)

            print(f"Condition {condition} ({condition_names[condition]}):")
            print(f"  Mean distance: {mean_distance:.2f}m")
            print(f"  Mean activation: {mean_activation:.4f}")
            print(f"  Distance-activation correlation: r={correlation:.3f}, p={p_value:.2e}")
            print()

    total_time = time.time() - start_time
    print(f"\nAll visualizations saved in '{args.output_dir}'")
    print(f"Total execution time: {total_time:.2f} seconds.")

if __name__ == '__main__':
    main()
