# utils/population_coding_viz.py
"""
图 4c: 智能体间距离的群体编码涌现 (Emergence of a Population Code for Inter-Agent Distance)
展示神经元群体通过多样化的调谐曲线来共同编码距离
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm
from scipy.ndimage import gaussian_filter
from scipy.stats import pearsonr
from scipy.interpolate import interp1d
from sklearn.preprocessing import MinMaxScaler
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional
import time
import seaborn as sns

# 导入项目模块
import sys
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config import Config
from models.social_grid_cell import SocialGridCellNetwork
from models.place_hd_cells import PlaceCellEnsemble, HeadDirectionCellEnsemble
from utils.functional_gating_viz import TrajectoryGenerator, ProximityData

# Nature子刊风格配置
plt.style.use('default')
plt.rcParams.update({
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'Helvetica', 'DejaVu Sans'],
    'font.size': 10,
    'axes.labelsize': 11,
    'axes.titlesize': 12,
    'xtick.labelsize': 9,
    'ytick.labelsize': 9,
    'legend.fontsize': 8,
    'figure.titlesize': 14,
    'figure.dpi': 150,
    'axes.linewidth': 1.0,
    'xtick.major.width': 1.0,
    'ytick.major.width': 1.0,
    'axes.spines.top': False,
    'axes.spines.right': False,
    'axes.edgecolor': 'black',
    'axes.linewidth': 1.2,
})

# Nature风格配色方案
NATURE_COLORS = {
    'close': '#E31A1C',      # 红色 - 近距离
    'mid': '#1F78B4',        # 蓝色 - 中距离  
    'far': '#33A02C',        # 绿色 - 远距离
    'population': '#FF7F00', # 橙色 - 群体
    'background': '#F0F0F0', # 浅灰色背景
    'grid': '#CCCCCC',       # 网格颜色
}

@dataclass
class DistanceCell:
    """距离细胞数据结构"""
    neuron_idx: int
    category: str  # 'close', 'mid', 'far'
    peak_distance: float
    max_activation: float
    tuning_curve: np.ndarray
    distances: np.ndarray
    activations: np.ndarray
    relative_positions: np.ndarray
    r_squared: float
    peak_width: float

class DistanceCellClassifier:
    """距离细胞分类器"""
    
    def __init__(self, close_threshold: float = 5.0, far_threshold: float = 12.0):
        self.close_threshold = close_threshold
        self.far_threshold = far_threshold
    
    def classify_neurons(self, model: SocialGridCellNetwork, config: Config, 
                        device: torch.device, num_reps: int = 400) -> Dict[str, List[DistanceCell]]:
        """分类所有神经元为近距离/中距离/远距离细胞"""
        
        trajectory_gen = TrajectoryGenerator(config)
        distance_cells = {'close': [], 'mid': [], 'far': []}
        
        print("Analyzing all neurons for distance tuning...")
        
        model.eval()
        with torch.no_grad():
            # 生成专门的邻近度分析轨迹
            trajs = trajectory_gen.get_proximity_analysis_trajectories(num_reps=num_reps)
            
            # 准备输入数据
            self_vel_input = torch.cat([
                torch.from_numpy(trajs['self_vel']).float(), 
                torch.from_numpy(trajs['self_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            self_init_pos = torch.from_numpy(trajs['self_pos'][:, 0, :]).float().to(device)
            self_init_hd = torch.from_numpy(np.arctan2(trajs['self_vel'][:, 0, 1], trajs['self_vel'][:, 0, 0])).float().to(device)
            
            peer_vel_input = torch.cat([
                torch.from_numpy(trajs['peer_vel']).float(), 
                torch.from_numpy(trajs['peer_ang_vel']).float().unsqueeze(-1)
            ], dim=-1).to(device)
            
            peer_init_pos = torch.from_numpy(trajs['peer_pos'][:, 0, :]).float().to(device)
            peer_init_hd = torch.from_numpy(np.arctan2(trajs['peer_vel'][:, 0, 1], trajs['peer_vel'][:, 0, 0])).float().to(device)

            # 前向传播
            outputs = model(self_vel_input, self_init_pos, self_init_hd, peer_vel_input, peer_init_pos, peer_init_hd)
            relational_activations = outputs['relational_activations'].cpu().numpy()  # (batch, seq, 64)
            
            # 计算距离和相对位置
            self_pos = trajs['self_pos']
            peer_pos = trajs['peer_pos']
            distances = np.linalg.norm(self_pos - peer_pos, axis=-1).flatten()
            relative_positions = (peer_pos - self_pos).reshape(-1, 2)
            
            # 分析每个神经元
            for neuron_idx in tqdm(range(relational_activations.shape[-1]), desc="Classifying neurons"):
                activations = relational_activations[:, :, neuron_idx].flatten()
                
                # 跳过静默神经元
                if np.std(activations) < 1e-6:
                    continue
                
                # 计算调谐曲线
                tuning_curve, distance_bins = self._compute_tuning_curve(distances, activations)
                
                # 找到峰值距离
                peak_idx = np.argmax(tuning_curve)
                peak_distance = distance_bins[peak_idx]
                max_activation = tuning_curve[peak_idx]
                
                # 计算R²
                r_squared = self._compute_r_squared(distances, activations, distance_bins, tuning_curve)
                
                # 计算峰值宽度（半高全宽）
                peak_width = self._compute_peak_width(tuning_curve, distance_bins)
                
                # 只保留有显著调谐的神经元（降低阈值以获得更多细胞）
                if r_squared > 0.05 and max_activation > np.mean(activations) + 0.5 * np.std(activations):
                    # 分类
                    if peak_distance <= self.close_threshold:
                        category = 'close'
                    elif peak_distance >= self.far_threshold:
                        category = 'far'
                    else:
                        category = 'mid'
                    
                    distance_cell = DistanceCell(
                        neuron_idx=neuron_idx,
                        category=category,
                        peak_distance=peak_distance,
                        max_activation=max_activation,
                        tuning_curve=tuning_curve,
                        distances=distance_bins,
                        activations=activations,
                        relative_positions=relative_positions,
                        r_squared=r_squared,
                        peak_width=peak_width
                    )
                    
                    distance_cells[category].append(distance_cell)
        
        # 按峰值距离排序
        for category in distance_cells:
            distance_cells[category].sort(key=lambda x: x.peak_distance)
        
        print(f"Classification results:")
        print(f"  Close-distance cells: {len(distance_cells['close'])}")
        print(f"  Mid-distance cells: {len(distance_cells['mid'])}")
        print(f"  Far-distance cells: {len(distance_cells['far'])}")
        
        return distance_cells
    
    def _compute_tuning_curve(self, distances: np.ndarray, activations: np.ndarray,
                            n_bins: int = 30) -> Tuple[np.ndarray, np.ndarray]:
        """计算距离调谐曲线"""
        max_dist = min(np.max(distances), 8.0)  # 扩展最大距离到25米
        bin_edges = np.linspace(0, max_dist, n_bins + 1)
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        
        tuning_curve = np.zeros(n_bins)
        for i in range(n_bins):
            mask = (distances >= bin_edges[i]) & (distances < bin_edges[i + 1])
            if np.sum(mask) > 0:
                tuning_curve[i] = np.mean(activations[mask])
        
        return tuning_curve, bin_centers
    
    def _compute_r_squared(self, distances: np.ndarray, activations: np.ndarray,
                          distance_bins: np.ndarray, tuning_curve: np.ndarray) -> float:
        """计算调谐曲线的R²"""
        # 插值得到每个距离点的预测激活
        interp_func = interp1d(distance_bins, tuning_curve, kind='linear', 
                              bounds_error=False, fill_value=0)
        predicted = interp_func(distances)
        
        # 计算R²
        ss_res = np.sum((activations - predicted) ** 2)
        ss_tot = np.sum((activations - np.mean(activations)) ** 2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        
        return max(0, r_squared)  # 确保非负
    
    def _compute_peak_width(self, tuning_curve: np.ndarray, distance_bins: np.ndarray) -> float:
        """计算峰值宽度（半高全宽）"""
        peak_val = np.max(tuning_curve)
        half_max = peak_val / 2
        
        # 找到半高点
        above_half = tuning_curve >= half_max
        if np.sum(above_half) < 2:
            return 0.0
        
        indices = np.where(above_half)[0]
        width = distance_bins[indices[-1]] - distance_bins[indices[0]]
        return width

def create_2d_activation_heatmap(distance_cell: DistanceCell, max_distance: float = 8.0,
                               grid_size: int = 60) -> np.ndarray:
    """创建2D激活热图"""
    heatmap = np.zeros((grid_size, grid_size))
    counts = np.zeros((grid_size, grid_size))

    # 将相对位置映射到网格坐标
    for i in range(len(distance_cell.relative_positions)):
        rel_pos = distance_cell.relative_positions[i]
        act = distance_cell.activations[i]

        # 将相对位置转换为网格坐标（中心为观察者位置）
        grid_x = int(np.clip((rel_pos[0] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))
        grid_y = int(np.clip((rel_pos[1] + max_distance) / (2 * max_distance) * grid_size, 0, grid_size - 1))

        heatmap[grid_y, grid_x] += act
        counts[grid_y, grid_x] += 1

    # 计算平均激活
    with np.errstate(divide='ignore', invalid='ignore'):
        avg_heatmap = np.where(counts > 0, heatmap / counts, 0)

    # 高斯平滑
    return gaussian_filter(avg_heatmap, sigma=1.2)

def plot_individual_distance_cell(distance_cell: DistanceCell, output_dir: str,
                                max_distance: float = 8.0):
    """绘制单个距离细胞的2D热图和1D调谐曲线"""

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # 左侧：2D激活热图
    heatmap = create_2d_activation_heatmap(distance_cell, max_distance)
    extent = [-max_distance, max_distance, -max_distance, max_distance]

    im = ax1.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                    aspect='equal', interpolation='bilinear')

    # 标记观察者位置
    ax1.scatter(0, 0, color='cyan', s=120, marker='*', edgecolors='black',
               linewidth=2, label='Observer', zorder=5)

    # 添加距离圆圈
    for radius in [5, 10, 15, 20]:
        circle = plt.Circle((0, 0), radius, fill=False, color='white',
                          linestyle='--', alpha=0.7, linewidth=1.5)
        ax1.add_patch(circle)
        ax1.text(radius*0.7, radius*0.7, f'{radius}m', color='white',
                fontsize=9, ha='center', va='center', weight='bold')

    ax1.set_xlabel('Relative X Position (m)', fontweight='bold')
    ax1.set_ylabel('Relative Y Position (m)', fontweight='bold')
    ax1.set_title(f'{distance_cell.category.title()}-Distance Cell (Neuron {distance_cell.neuron_idx})\n2D Activation Map',
                 fontweight='bold', pad=15)
    ax1.legend(loc='upper right', frameon=True, fancybox=False, shadow=False)
    ax1.grid(True, linestyle=':', alpha=0.3, color='white')

    # 添加colorbar
    cbar = plt.colorbar(im, ax=ax1, shrink=0.8)
    cbar.set_label('Neural Activation', rotation=270, labelpad=15, fontweight='bold')

    # 右侧：1D距离调谐曲线
    color = NATURE_COLORS[distance_cell.category]

    ax2.plot(distance_cell.distances, distance_cell.tuning_curve,
             color=color, linewidth=3, alpha=0.8, label='Tuning Curve')
    ax2.fill_between(distance_cell.distances, distance_cell.tuning_curve,
                     alpha=0.3, color=color)

    # 标记峰值
    peak_idx = np.argmax(distance_cell.tuning_curve)
    ax2.scatter(distance_cell.peak_distance, distance_cell.max_activation,
               color=color, s=100, zorder=5, edgecolors='black', linewidth=2)
    ax2.axvline(distance_cell.peak_distance, color=color, linestyle='--', alpha=0.7)

    ax2.set_xlabel('Inter-Agent Distance (m)', fontweight='bold')
    ax2.set_ylabel('Neural Activation', fontweight='bold')
    ax2.set_title(f'Distance Tuning Curve\nPeak: {distance_cell.peak_distance:.1f}m, R²: {distance_cell.r_squared:.3f}',
                 fontweight='bold', pad=15)
    ax2.grid(True, linestyle=':', alpha=0.5)
    ax2.set_xlim(0, max_distance)
    ax2.set_ylim(bottom=0)

    # 添加统计信息
    stats_text = f'Peak Distance: {distance_cell.peak_distance:.1f}m\nMax Activation: {distance_cell.max_activation:.3f}\nR²: {distance_cell.r_squared:.3f}\nPeak Width: {distance_cell.peak_width:.1f}m'
    ax2.text(0.95, 0.95, stats_text, transform=ax2.transAxes, fontsize=9,
            verticalalignment='top', horizontalalignment='right',
            bbox=dict(boxstyle='round,pad=0.4', facecolor='white', alpha=0.9, edgecolor='gray'))

    plt.tight_layout()

    # 保存图形
    category_dir = os.path.join(output_dir, f"{distance_cell.category}_distance_cells")
    os.makedirs(category_dir, exist_ok=True)

    filename = f"distance_cell_neuron_{distance_cell.neuron_idx}_{distance_cell.category}.pdf"
    save_path = os.path.join(category_dir, filename)

    plt.savefig(save_path, format='pdf', bbox_inches='tight', dpi=300,
                facecolor='white', edgecolor='none')
    plt.close(fig)

    return save_path

def plot_population_tiling(distance_cells: Dict[str, List[DistanceCell]],
                          output_dir: str, max_distance: float = 8.0):
    """绘制群体编码的瓦片式覆盖"""

    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    # 收集所有距离细胞
    all_cells = []
    for category in ['close', 'mid', 'far']:
        all_cells.extend(distance_cells[category])

    # 按峰值距离排序
    all_cells.sort(key=lambda x: x.peak_distance)

    print(f"Plotting population tiling with {len(all_cells)} distance cells...")

    # 创建颜色映射
    colors = plt.cm.viridis(np.linspace(0, 1, len(all_cells)))

    # 绘制所有调谐曲线
    for i, cell in enumerate(all_cells):
        # 归一化调谐曲线
        normalized_curve = cell.tuning_curve / np.max(cell.tuning_curve) if np.max(cell.tuning_curve) > 0 else cell.tuning_curve

        # 根据类别选择颜色
        if cell.category == 'close':
            color = NATURE_COLORS['close']
            alpha = 0.8
        elif cell.category == 'mid':
            color = NATURE_COLORS['mid']
            alpha = 0.8
        else:
            color = NATURE_COLORS['far']
            alpha = 0.8

        ax.plot(cell.distances, normalized_curve, color=color, alpha=alpha, linewidth=2)

    # 添加类别图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color=NATURE_COLORS['close'], lw=3, label=f'Close-Distance Cells (n={len(distance_cells["close"])})'),
        Line2D([0], [0], color=NATURE_COLORS['mid'], lw=3, label=f'Mid-Distance Cells (n={len(distance_cells["mid"])})'),
        Line2D([0], [0], color=NATURE_COLORS['far'], lw=3, label=f'Far-Distance Cells (n={len(distance_cells["far"])})')
    ]

    ax.legend(handles=legend_elements, loc='upper right', frameon=True,
             fancybox=False, shadow=False, fontsize=11)

    ax.set_xlabel('Inter-Agent Distance (m)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Normalized Neural Activation', fontsize=14, fontweight='bold')
    ax.set_title('Population Code for Inter-Agent Distance\nTiling of Distance Space by Neural Tuning Curves',
                fontsize=16, fontweight='bold', pad=20)

    ax.grid(True, linestyle=':', alpha=0.5)
    ax.set_xlim(0, max_distance)
    ax.set_ylim(0, 1.1)

    # 添加统计信息
    total_cells = len(all_cells)
    peak_distances = [cell.peak_distance for cell in all_cells]
    coverage_text = f'Total Distance Cells: {total_cells}\nDistance Range: {np.min(peak_distances):.1f} - {np.max(peak_distances):.1f}m\nMean Peak Distance: {np.mean(peak_distances):.1f}m'

    ax.text(0.02, 0.98, coverage_text, transform=ax.transAxes, fontsize=11,
           verticalalignment='top', horizontalalignment='left',
           bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9, edgecolor='gray'))

    plt.tight_layout()

    # 保存图形
    filename = "population_distance_coding_tiling.pdf"
    save_path = os.path.join(output_dir, filename)

    plt.savefig(save_path, format='pdf', bbox_inches='tight', dpi=300,
                facecolor='white', edgecolor='none')
    plt.close(fig)

    print(f"Population tiling plot saved to: {save_path}")
    return save_path

def create_combined_figure(distance_cells: Dict[str, List[DistanceCell]],
                          output_dir: str, max_distance: float = 8.0):
    """创建2x2组合图展示距离编码的涌现"""

    fig = plt.figure(figsize=(16, 12))

    # 选择每个类别的最佳代表
    representatives = {}
    for category in ['close', 'mid', 'far']:
        if distance_cells[category]:
            # 选择R²最高的神经元作为代表
            best_cell = max(distance_cells[category], key=lambda x: x.r_squared)
            representatives[category] = best_cell

    # 面板 (i): 近距离细胞
    if 'close' in representatives:
        ax1 = plt.subplot(2, 2, 1)
        cell = representatives['close']
        heatmap = create_2d_activation_heatmap(cell, max_distance)
        extent = [-max_distance, max_distance, -max_distance, max_distance]

        im1 = ax1.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                        aspect='equal', interpolation='bilinear')
        ax1.scatter(0, 0, color='cyan', s=80, marker='*', edgecolors='black', linewidth=1.5)

        # 添加距离圆圈
        for radius in [5, 10, 15]:
            circle = plt.Circle((0, 0), radius, fill=False, color='white',
                              linestyle='--', alpha=0.6, linewidth=1)
            ax1.add_patch(circle)

        ax1.set_title(f'(i) Close-Distance Cell\nNeuron {cell.neuron_idx}, Peak: {cell.peak_distance:.1f}m',
                     fontweight='bold', fontsize=11)
        ax1.set_xlabel('Relative X (m)', fontweight='bold')
        ax1.set_ylabel('Relative Y (m)', fontweight='bold')

        # 面板 (ii): 近距离细胞的调谐曲线
        ax2 = plt.subplot(2, 2, 2)
        ax2.plot(cell.distances, cell.tuning_curve, color=NATURE_COLORS['close'],
                linewidth=3, alpha=0.8)
        ax2.fill_between(cell.distances, cell.tuning_curve, alpha=0.3, color=NATURE_COLORS['close'])
        ax2.axvline(cell.peak_distance, color=NATURE_COLORS['close'], linestyle='--', alpha=0.7)

        ax2.set_title(f'(ii) Close-Distance Tuning\nR² = {cell.r_squared:.3f}',
                     fontweight='bold', fontsize=11)
        ax2.set_xlabel('Distance (m)', fontweight='bold')
        ax2.set_ylabel('Activation', fontweight='bold')
        ax2.grid(True, alpha=0.3)
        ax2.set_xlim(0, max_distance)

    # 面板 (iii): 中距离细胞
    if 'mid' in representatives:
        ax3 = plt.subplot(2, 2, 3)
        cell = representatives['mid']
        heatmap = create_2d_activation_heatmap(cell, max_distance)

        im3 = ax3.imshow(heatmap, cmap='jet', origin='lower', extent=extent,
                        aspect='equal', interpolation='bilinear')
        ax3.scatter(0, 0, color='cyan', s=80, marker='*', edgecolors='black', linewidth=1.5)

        for radius in [10, 20]:
            circle = plt.Circle((0, 0), radius, fill=False, color='white',
                              linestyle='--', alpha=0.6, linewidth=1)
            ax3.add_patch(circle)

        ax3.set_title(f'(iii) Mid-Distance Cell\nNeuron {cell.neuron_idx}, Peak: {cell.peak_distance:.1f}m',
                     fontweight='bold', fontsize=11)
        ax3.set_xlabel('Relative X (m)', fontweight='bold')
        ax3.set_ylabel('Relative Y (m)', fontweight='bold')

    # 面板 (iv): 群体编码瓦片式覆盖
    ax4 = plt.subplot(2, 2, 4)

    # 收集所有距离细胞
    all_cells = []
    for category in ['close', 'mid', 'far']:
        all_cells.extend(distance_cells[category])
    all_cells.sort(key=lambda x: x.peak_distance)

    # 绘制所有调谐曲线
    for cell in all_cells:
        normalized_curve = cell.tuning_curve / np.max(cell.tuning_curve) if np.max(cell.tuning_curve) > 0 else cell.tuning_curve
        color = NATURE_COLORS[cell.category]
        ax4.plot(cell.distances, normalized_curve, color=color, alpha=0.7, linewidth=1.5)

    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color=NATURE_COLORS['close'], lw=2, label=f'Close (n={len(distance_cells["close"])})'),
        Line2D([0], [0], color=NATURE_COLORS['mid'], lw=2, label=f'Mid (n={len(distance_cells["mid"])})'),
        Line2D([0], [0], color=NATURE_COLORS['far'], lw=2, label=f'Far (n={len(distance_cells["far"])})')
    ]
    ax4.legend(handles=legend_elements, loc='upper right', fontsize=9)

    ax4.set_title('(iv) Population Code Tiling\nDistance Space Coverage',
                 fontweight='bold', fontsize=11)
    ax4.set_xlabel('Distance (m)', fontweight='bold')
    ax4.set_ylabel('Normalized Activation', fontweight='bold')
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(0, max_distance)
    ax4.set_ylim(0, 1.1)

    # 设置整体标题
    fig.suptitle('Emergence of a Population Code for Inter-Agent Distance',
                fontsize=18, fontweight='bold', y=0.95)

    plt.tight_layout()
    plt.subplots_adjust(top=0.90, hspace=0.3, wspace=0.3)

    # 保存图形
    filename = "population_distance_coding_emergence.pdf"
    save_path = os.path.join(output_dir, filename)

    plt.savefig(save_path, format='pdf', bbox_inches='tight', dpi=300,
                facecolor='white', edgecolor='none')
    plt.close(fig)

    print(f"Combined figure saved to: {save_path}")
    return save_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Population Coding Visualization for Inter-Agent Distance")
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to the trained best_model.pth file.')
    parser.add_argument('--output_dir', type=str, default='population_coding_viz',
                       help='Directory to save visualizations.')
    parser.add_argument('--num_reps', type=int, default=400,
                       help='Number of trajectory repetitions for analysis.')
    parser.add_argument('--close_threshold', type=float, default=5.0,
                       help='Threshold for close-distance cells (meters).')
    parser.add_argument('--far_threshold', type=float, default=12.0,
                       help='Threshold for far-distance cells (meters).')
    parser.add_argument('--max_distance', type=float, default=25.0,
                       help='Maximum distance for visualization (meters).')
    args = parser.parse_args()

    start_time = time.time()
    os.makedirs(args.output_dir, exist_ok=True)

    # 加载配置和模型
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    print("Loading model...")
    place_cells = PlaceCellEnsemble(
        n_cells=config.PLACE_CELLS_N,
        scale=config.PLACE_CELLS_SCALE,
        pos_min=0, pos_max=config.ENV_SIZE,
        seed=config.SEED
    )
    hd_cells = HeadDirectionCellEnsemble(
        n_cells=config.HD_CELLS_N,
        concentration=config.HD_CELLS_CONCENTRATION,
        seed=config.SEED
    )

    model_config = {
        'HIDDEN_SIZE': config.HIDDEN_SIZE,
        'LATENT_DIM': config.LATENT_DIM,
        'dropout_rate': config.DROPOUT_RATE,
        'ego_token_size': getattr(config, 'ego_token_size', 4)
    }
    model = SocialGridCellNetwork(place_cells, hd_cells, model_config)

    # 加载模型权重
    state_dict = torch.load(args.model_path, map_location=device, weights_only=False)
    if all(key.startswith('module.') for key in state_dict.keys()):
        state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}
    model.load_state_dict(state_dict)
    model.to(device)
    model.eval()

    print(f"Model loaded. Relational head has {model.relational_head[0].out_features} neurons.")

    # 分类距离细胞
    classifier = DistanceCellClassifier(args.close_threshold, args.far_threshold)
    distance_cells = classifier.classify_neurons(model, config, device, args.num_reps)

    # 保存个体距离细胞
    print("\nSaving individual distance cell visualizations...")
    for category in ['close', 'mid', 'far']:
        for cell in distance_cells[category][:5]:  # 保存每类前5个最佳的
            plot_individual_distance_cell(cell, args.output_dir, args.max_distance)

    # 创建群体编码瓦片图
    print("\nCreating population tiling visualization...")
    plot_population_tiling(distance_cells, args.output_dir, args.max_distance)

    # 创建组合图
    print("\nCreating combined figure...")
    create_combined_figure(distance_cells, args.output_dir, args.max_distance)

    total_time = time.time() - start_time
    print(f"\nAll visualizations saved in '{args.output_dir}'")
    print(f"Total execution time: {total_time:.2f} seconds.")

if __name__ == '__main__':
    main()
